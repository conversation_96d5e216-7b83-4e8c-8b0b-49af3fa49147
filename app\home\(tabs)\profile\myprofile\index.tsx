import * as ImagePicker from "expo-image-picker";
import BackIcon from "../../../../../assets/OrderDetails/backIcon.svg";
import CalendarIcon from "../../../../../assets/ProfileAsser/SectionIcon/UserprofileAssert/calander.svg";
import CamaraIcon from "../../../../../assets/icons/bottemSheetIcon/camara.svg";
import Close from "../../../../../assets/icons/bottemSheetIcon/Close.svg";
import Close2 from "../../../../../assets/icons/bottemSheetIcon/Close2.svg";
import DeleteIcon from "../../../../../assets/icons/bottemSheetIcon/delete.svg";
import DropDownComponent from "../../../../../component/DropDownComponent";
import EditIcon from "../../../../../assets/ProfileAsser/SectionIcon/UserprofileAssert/Group 1321316369.svg";
import EyeIcon from "../../../../../assets/icons/bottemSheetIcon/Eye.svg";
import GalleryIcon from "../../../../../assets/icons/bottemSheetIcon/gallery.svg";
import RBSheet from "react-native-raw-bottom-sheet";
import React, { useEffect, useRef, useState } from "react";
import Svg, { Circle, Rect, Text as SvgText } from "react-native-svg";
import TextinputWithEdit from "../../../../../component/TextinputWithEdit";
import useTenStackHook from "@/hooks/TenStackHook/TenStackHook";
import useTenStackMutate from "@/hooks/useTenStackMutate/useTenStackMutate";
import { LinearGradient } from "expo-linear-gradient";
import { router } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";
import { useConformNumberEmail } from "../../../../../store";

import {
  Controller,
  FieldValues,
  RegisterOptions,
  useForm,
} from "react-hook-form";

import DateTimePicker, {
  DateTimePickerEvent,
} from "@react-native-community/datetimepicker";

import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Image,
  Modal,
  Alert,
} from "react-native";

const index = () => {
  const {
    control,
    setValue,
    watch,
    handleSubmit,
    formState: { errors },
  } = useForm();
  const refRBSheet = useRef<any>();
  const [showprofile, setshowprofile] = useState(false);
  const formData = new FormData();
  const { data: ProfileData, isFetching: ProfileLoading } = useTenStackHook<
    {},
    { data: UserEntity }
  >({
    key: "getProfile",
    canSave: true,
    endpoint: "/getProfile",
  });

  const { mutate: UpdateProfileImage } = useTenStackMutate({
    endpoint: "auth/updateProfileImage",
    invalidateQueriesKey: ["getProfile"],
  });

  const { mutate: UpdateProfile } = useTenStackMutate({
    endpoint: "auth/updateProfile",
    invalidateQueriesKey: ["getProfile"],
  });
  const { mutate: CallFunction } = useTenStackMutate({
    endpoint: "auth/deleteProfileImage",
    invalidateQueriesKey: ["getProfile"],
  });

  const pickImage = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.All,
      quality: 1,
    });

    if (!result.canceled) {
      formData.append("image", {
        uri: result.assets[0].uri,
        type: result.assets[0].mimeType,
        name: result.assets[0].uri,
      } as any);
      UpdateProfileImage(formData);
    }
  };
  const openCamera = async () => {
    let result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.All,
      quality: 1,
    });

    if (!result.canceled) {
      formData.append("image", {
        uri: result.assets[0].uri,
        type: result.assets[0].mimeType,
        name: result.assets[0].uri,
      } as any);
      UpdateProfileImage(formData);
    }
  };

  const [date, setdate] = useState<Date | undefined>(undefined);
  const [showpicker, setshowpicker] = useState(false);
  const [datevale, setdatevale] = useState("");

  const [JoinDate, setJoinDate] = useState<Date | undefined>(undefined);
  const [showpickerJoinDate, setshowpickerJoinDate] = useState(false);
  const [dateJoinDate, setdateJoinDate] = useState("");
  const toggledatepicker = () => {
    setshowpicker(!showpicker);
  };
  const toggledatepickerJoinDate = () => {
    setshowpickerJoinDate(!showpicker);
  };
  const change = (event: DateTimePickerEvent, selectedDate?: Date) => {
    if (event.type === "set" && selectedDate) {
      const currentdatestringt = DataFormat(selectedDate);
      setdate(selectedDate);
      setdatevale(currentdatestringt);
      setshowpicker(false);
    } else {
      setshowpicker(false);
    }
  };
  const JoinDateChange = (event: DateTimePickerEvent, selectedDate?: Date) => {
    if (event.type === "set" && selectedDate) {
      const currentdatestringt = DataFormat(selectedDate);
      setJoinDate(selectedDate);
      setdateJoinDate(currentdatestringt);
      setshowpickerJoinDate(false);
    } else {
      setshowpickerJoinDate(false);
    }
  };
  const DataFormat = (date = new Date()) => {
    let day = String(date.getDate()).padStart(2, "0");
    let month = String(date.getMonth() + 1).padStart(2, "0"); // Months are zero based
    let year = date.getFullYear();

    return `${day}/${month}/${year}`;
  };

  const { phone, email, setPhone, setEmail } = useConformNumberEmail(
    (state) => state
  );

  const UpdateProfileData = async (data: any) => {
    UpdateProfile(
      {
        name: data.name,
        email: data.email,
        phone: phone,
        dob: date,
        date_of_joinning: JoinDate,
        gender: data.gender,
      },
      {
        onSuccess: (responce) => {
          if (responce.status === 0) {
            Alert.alert("Error", responce?.msg);
          }
          Alert.alert("success", responce?.msg);
        },
      }
    );
  };

  useEffect(() => {
    if (!ProfileLoading) {
      setValue("name", ProfileData?.data?.name);
      setValue("number", ProfileData?.data?.phone);
      setValue("email", ProfileData?.data?.email);
      setValue("gender", ProfileData?.data?.gender);
      const dob = DataFormat(
        new Date(ProfileData?.data?.dob ? ProfileData?.data?.dob : Date())
      );
      setdatevale(dob);

      setdate(
        new Date(
          ProfileData?.data?.date_of_joinning
            ? ProfileData?.data?.date_of_joinning
            : new Date()
        )
      );
      const ApiJoinDate = DataFormat(
        new Date(
          ProfileData?.data?.date_of_joinning
            ? ProfileData?.data?.date_of_joinning
            : Date()
        )
      );
      setdateJoinDate(ApiJoinDate);
      setJoinDate(
        new Date(
          ProfileData?.data?.date_of_joinning
            ? ProfileData?.data?.date_of_joinning
            : Date()
        )
      );
    }
  }, [ProfileLoading]);
  console.log(ProfileData?.data?.image);
  return (
    <SafeAreaView className="flex-1">
      <ScrollView>
        <View className="items-center justify-center relative mt-6">
          <TouchableOpacity
            className="absolute left-[3%]"
            onPress={() => {
              router.back();
            }}
          >
            <BackIcon />
          </TouchableOpacity>
          <View>
            <Text className="font-[600] text-[20px] leading-[30px]">
              Your Profile
            </Text>
          </View>
        </View>
        <View className="mt-8 items-center justify-center mb-6">
          <View className="relative">
            {ProfileData?.data?.image ? (
              <>
                <Image
                  source={{
                    uri: ProfileData?.data?.image,
                  }}
                  className="h-[120px] w-[120px] rounded-full "
                />
              </>
            ) : (
              <>
                <EditableSVG
                  text={
                    ProfileData?.data?.name
                      ? ProfileData?.data?.name.split("")[0]
                      : ""
                  }
                  height={120}
                  width={120}
                />
              </>
            )}
            <TouchableOpacity
              onPress={() => {
                (refRBSheet as any).current.open();
              }}
              className="absolute bottom-0 left-0"
            >
              <EditIcon />
            </TouchableOpacity>
          </View>
        </View>
        <View className="mt-4">
          <TextFieldComponent
            text={"Name"}
            defaultval={ProfileLoading ? "" : ProfileData?.data?.name}
            control={control}
            name={"name"}
          />
        </View>
        {ProfileLoading ? (
          <></>
        ) : (
          <>
            {/* <View key={1} className="px-4">
              <TextinputWithEdit
                text={"Email ID"}
                placeholder={ProfileData?.data?.email}
                ApiData={ProfileData?.data}
                watch={watch}
                control={control}
                name={"email"}
                IsVerified={ProfileData?.data?.is_email_verified}
                changes={ProfileData?.data?.email !== watch("email")}
              />
            </View> */}
            <View className="mt-4">
              <TextFieldComponent
                text={"Email ID"}
                defaultval={
                  ProfileData?.data?.email ? "" : ProfileData?.data?.email
                }
                control={control}
                name={"email"}
              />
            </View>
            {errors.email && (
              <Text className="text-red-500 text-center">
                {(errors as any).email.message}
              </Text>
            )}
          </>
        )}
        {ProfileLoading ? (
          <></>
        ) : (
          <>
            <View className="mt-2 px-4">
              <TextinputWithEdit
                text={"Phone Number"}
                placeholder={ProfileData?.data?.phone}
                ApiData={ProfileData?.data}
                watch={watch}
                control={control}
                name={"number"}
                IsVerified={ProfileData?.data?.is_phone_verified}
                changes={ProfileData?.data?.phone !== watch("number")}
              />
            </View>
          </>
        )}

        {/* Date of Birth Textfield Area */}

        <View className="mt-4 px-4">
          <Text>Date of Birth</Text>
          {showpicker && (
            <DateTimePicker
              maximumDate={new Date()}
              value={date ? date : new Date()}
              mode="date"
              onChange={change}
            />
          )}
          <TouchableOpacity
            onPress={() => {
              toggledatepicker();
            }}
          >
            <View className="rounded-[4px] px-2 flex-row justify-center items-center mt-2 border-[1px] h-[40px] border-[#ACB9D5]">
              <TextInput
                className="flex-1 text-[#000] font-Pop"
                editable={false}
                value={datevale}
              />
              <CalendarIcon />
            </View>
          </TouchableOpacity>
        </View>

        {/* Date of Birth Textfield Area */}

        <View className="mt-4 px-4">
          <Text>Joining Date</Text>
          {showpickerJoinDate && (
            <DateTimePicker
              maximumDate={new Date()}
              value={JoinDate ? JoinDate : new Date()}
              mode="date"
              onChange={JoinDateChange}
            />
          )}
          <TouchableOpacity
            onPress={() => {
              toggledatepickerJoinDate();
            }}
          >
            <View className="rounded-[4px] px-2 flex-row justify-center items-center mt-2 border-[1px] h-[40px] border-[#ACB9D5]">
              <TextInput
                className="flex-1 text-[#000] font-Pop"
                editable={false}
                value={dateJoinDate}
              />
              <CalendarIcon />
            </View>
          </TouchableOpacity>
        </View>
        {ProfileLoading ? (
          <></>
        ) : (
          <>
            <View>
              <View className="px-4">
                <DropDownComponent
                  control={control}
                  setvaluefun={setValue}
                  name={"gender"}
                  data={[
                    { label: "Male", value: "1" },
                    { label: "Female", value: "2" },
                    { label: "Other", value: "3" },
                  ]}
                  text={"Gender"}
                  placeholder={"Select gender"}
                  style={undefined}
                  defaul={ProfileData?.data?.gender}
                  rules={undefined}
                />
              </View>
            </View>
          </>
        )}
      </ScrollView>
      <View className="px-4 mb-4">
        <TouchableOpacity
          onPress={() => {
            handleSubmit(UpdateProfileData)();
          }}
          className="mt-4 h-[44px] items-center justify-center bg-[#00660A]"
        >
          <Text className="font-[400] leading-[24px] text-[#fff]">
            Update Profile
          </Text>
        </TouchableOpacity>
      </View>

      {ProfileLoading ? (
        <></>
      ) : (
        <>
          <View className="">
            <ShowUserProfile
              showprofile={showprofile}
              setfun={setshowprofile}
              img={
                ProfileData?.data?.image
                  ? { uri: ProfileData?.data?.image }
                  : undefined
              }
              LogoFirstLetter={
                ProfileData?.data?.name
                  ? ProfileData?.data?.name.split("")[0]
                  : ""
              }
            />
          </View>
        </>
      )}

      <RBSheet
        ref={refRBSheet}
        customStyles={{
          container: {
            borderRadius: 20,
            height: "35%",
          },
          draggableIcon: {
            backgroundColor: "#000",
          },
        }}
        customModalProps={{
          statusBarTranslucent: true,
        }}
        customAvoidingViewProps={{
          enabled: false,
        }}
      >
        <View className="px-6 py-6 bg-[#EDEEED]">
          <View className="items-end">
            <TouchableOpacity
              onPress={() => {
                (refRBSheet as any).current.close();
              }}
            >
              <Close />
            </TouchableOpacity>
          </View>

          <View className="bg-[#FFFFFF] mt-3 px-5">
            <TouchableOpacity
              onPress={() => {
                setshowprofile(true);
              }}
              className="flex-row items-center justify-between h-[56px]"
            >
              <Text className="font-[400] text-[16px] leading-[20px] text-[#627164]">
                View Profile Picture
              </Text>
              <EyeIcon />
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                pickImage();
              }}
              className="flex-row items-center justify-between h-[56px]"
            >
              <Text className="font-[400] text-[16px] leading-[20px] text-[#627164]">
                Upload from Gallery
              </Text>
              <GalleryIcon />
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                openCamera();
              }}
              className="flex-row items-center justify-between h-[56px]"
            >
              <Text className="font-[400] text-[16px] leading-[20px] text-[#627164]">
                Take Photo
              </Text>
              <CamaraIcon />
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                CallFunction({});
              }}
              className="flex-row items-center justify-between h-[56px]"
            >
              <Text className="font-[400] text-[16px] leading-[20px] text-[#627164]">
                Delete Photo
              </Text>
              <DeleteIcon />
            </TouchableOpacity>
          </View>
        </View>
      </RBSheet>
    </SafeAreaView>
  );
};

const TextFieldComponent = ({
  text,
  defaultval,
  name,
  control,
  rules,
}: {
  text: string;
  defaultval?: string;
  name: string;
  control: any;
  rules?:
    | Omit<
        RegisterOptions<FieldValues, string>,
        "valueAsNumber" | "valueAsDate" | "setValueAs" | "disabled"
      >
    | undefined;
}) => {
  return (
    <>
      <View className="px-4">
        <Text>{text}</Text>
        <Controller
          name={name}
          rules={rules}
          control={control}
          defaultValue={defaultval}
          render={({ field: { value, onChange } }) => {
            return (
              <TextInput
                value={value}
                onChangeText={(text) => {
                  onChange(text);
                }}
                className="h-[40px] border-[1px] border-[#ACB9D5] rounded-[4px] pl-2 mt-2 text-[#000]"
              />
            );
          }}
        />
      </View>
    </>
  );
};

export const ShowUserProfile = ({
  showprofile,
  setfun,
  img,
  LogoFirstLetter,
}: {
  showprofile: boolean;
  setfun: any;
  img: any;
  LogoFirstLetter?: string;
}) => {
  return (
    <>
      <Modal transparent={true} visible={showprofile}>
        <View className="flex-1">
          <LinearGradient
            colors={["#000", "#000"]}
            start={[0, 0]}
            style={{
              position: "absolute",
              left: 0,
              right: 0,
              top: 0,
              bottom: 0,
              zIndex: 1,
              flex: -1,
              opacity: 0.8,
            }}
          />
          <View className="flex-1 items-center z-30 justify-center ">
            {!img ? (
              <View className="flex-1 items-center justify-center">
                {LogoFirstLetter && LogoFirstLetter.length === 1 ? (
                  <>
                    <EditableSVG
                      text={LogoFirstLetter}
                      height={300}
                      width={300}
                    />
                  </>
                ) : (
                  <>
                    {LogoFirstLetter && LogoFirstLetter.length > 1 ? (
                      <>
                        <EditableSVGFullNameForShowProfile
                          text={LogoFirstLetter}
                          height={300}
                          width={500}
                        />
                      </>
                    ) : (
                      <></>
                    )}
                  </>
                )}
              </View>
            ) : (
              <>
                <Image
                  source={img}
                  className="w-[100%] h-[100%]"
                  style={{ objectFit: "contain" }}
                />
              </>
            )}
            <TouchableOpacity
              onPress={() => {
                setfun(false);
              }}
              className="absolute z-50 top-14 right-8"
            >
              <Close2 width={26} height={26} fill={"#fff"} />
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </>
  );
};

export const EditableSVG = ({ text, width, height }: any) => {
  return (
    <Svg
      width={width}
      height={height}
      viewBox="0 0 60 60"
      fill="none"
      justifyContent="center"
      xmlns="http://www.w3.org/2000/svg"
    >
      <Circle cx="30" cy="30" r="30" fill="#00660A" />
      <SvgText
        x={31}
        y={45}
        textAnchor="middle"
        fill={"#FFFFFF"}
        fontSize={42}
        fontFamily="Arial"
      >
        {text}
      </SvgText>
    </Svg>
  );
};
export const EditableSVGFullName = ({ text, width, height }) => {
  return (
    <Svg
      width={"100%"}
      height={height}
      viewBox={`0 0 100% ${height}`}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <Rect x="0" y="0" width={"100%"} height={height} fill="#FFFFFF" />
      <SvgText
        x="50%"
        y="50%"
        textAnchor="middle"
        fill="#00660A"
        fontSize={80}
        fontFamily="Arial"
        alignmentBaseline="middle" // Align text to middle of the y coordinate
      >
        {text}
      </SvgText>
    </Svg>
  );
};
export const EditableSVGFullNameForShowProfile = ({ text, width, height }) => {
  return (
    <Svg
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <Rect x="0" y="0" width={width} height={height} fill={"#FFFFFF"} />
      <SvgText
        x="50%"
        y="50%"
        textAnchor="middle"
        fill="#00660A"
        fontSize={80}
        fontFamily="Arial"
        alignmentBaseline="middle" // Align text to middle of the y coordinate
      >
        {text}
      </SvgText>
    </Svg>
  );
};

export default index;
